<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Select from '$lib/components/ui/select';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { AlertTriangle, CheckCircle } from '@lucide/svelte';
	import type { ColumnMapping, ProcessedRow, ExcelRow } from '$lib/budget_import_utils';
	import {
		matchColumns,
		validateColumnMapping,
		APPLICATION_FIELDS,
	} from '$lib/budget_import_utils';

	interface Props {
		headers: string[];
		sampleRows: ProcessedRow[];
		rawPreviewData?: ExcelRow[];
		onMappingChange: (mapping: ColumnMapping) => void;
		onNext: () => void;
		onBack: () => void;
	}

	let { headers, sampleRows, rawPreviewData, onMappingChange, onNext, onBack }: Props = $props();

	// Auto-detect column mapping
	let columnMapping = $state<ColumnMapping>(matchColumns(headers));

	// Validation
	let validation = $derived(validateColumnMapping(columnMapping));

	// Update parent when mapping changes
	$effect(() => {
		onMappingChange(columnMapping);
	});

	function updateMapping(fieldKey: keyof ColumnMapping, headerIndex: number | null) {
		// Clear any existing mapping for this field
		columnMapping[fieldKey] = undefined;

		// Clear any other field that might be using this header index
		if (headerIndex !== null) {
			for (const [key, value] of Object.entries(columnMapping)) {
				if (value === headerIndex && key !== fieldKey) {
					columnMapping[key as keyof ColumnMapping] = undefined;
				}
			}
			columnMapping[fieldKey] = headerIndex;
		}
	}

	function getSampleValues(headerIndex: number | null): string[] {
		if (headerIndex === null || headerIndex === undefined) return [];

		// Use raw preview data if available, otherwise fall back to processed rows
		if (rawPreviewData && rawPreviewData.length > 0) {
			return rawPreviewData
				.slice(1) // Skip header row
				.map((row) => {
					const value = row[headerIndex];
					return value ? String(value).trim() : '';
				})
				.filter((v) => v && v.length > 0)
				.slice(0, 3); // Show max 3 sample values
		}

		// Fallback to processed rows using raw values
		return sampleRows
			.slice(0, 5)
			.map((row) => {
				// Extract value from raw values array
				if (headerIndex >= row.rawValues.length) return '';
				const value = row.rawValues[headerIndex];
				if (value === null || value === undefined) return '';
				return String(value);
			})
			.filter((v) => v && v.trim())
			.slice(0, 3);
	}

	function getAvailableHeaders(): Array<{ value: number; label: string }> {
		return headers.map((header, index) => ({
			value: index,
			label: header || `Column ${index + 1}`,
		}));
	}

	function isFieldRequired(field: (typeof APPLICATION_FIELDS)[0]): boolean {
		return (
			field.required ||
			(field.conditionallyRequired ? field.conditionallyRequired(columnMapping) : false)
		);
	}
</script>

<div class="space-y-6">
	<div>
		<h2 class="mb-2 text-xl font-semibold">Step 3: Map Columns</h2>
		<p class="text-muted-foreground">
			Map your Excel columns to the required application fields. Required fields must have a column
			selected.
		</p>
		{#if columnMapping.unit_rate !== undefined}
			<Alert class="mt-4">
				<AlertTriangle class="h-4 w-4" />
				<AlertDescription>
					<p><strong>CostX Import Note</strong></p>
					<p>
						The "Rate" column from your CostX export is being mapped to "Unit Rate" and represents
						the total <strong>Supplied & Installed Rate</strong> (not just materials cost). The system
						will automatically set this as a manual override since no separate material rate is provided.
					</p>
				</AlertDescription>
			</Alert>
		{/if}
	</div>

	{#if !validation.isValid}
		<Alert variant="destructive">
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>
				<ul class="list-inside list-disc">
					{#each validation.errors as error (error)}
						<li>{error}</li>
					{/each}
				</ul>
			</AlertDescription>
		</Alert>
	{:else}
		<Alert>
			<CheckCircle class="h-4 w-4" />
			<AlertDescription>
				All required fields are mapped correctly. You can proceed to the next step.
			</AlertDescription>
		</Alert>
	{/if}

	<div class="rounded-md border">
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead class="w-1/3">Application Field</TableHead>
					<TableHead class="w-1/6">Status</TableHead>
					<TableHead class="w-1/2">Selected Excel Column</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#each APPLICATION_FIELDS as field (field.key)}
					{@const hasMatchedColumn =
						columnMapping[field.key] !== undefined && columnMapping[field.key] !== null}
					<!-- Main mapping row -->
					<TableRow>
						<TableCell class="font-medium">
							{field.label}
						</TableCell>
						<TableCell>
							{#if isFieldRequired(field)}
								<Badge
									variant="outline"
									class={{
										'animate-pulse bg-red-500 text-white ': !hasMatchedColumn,
										'border-red-500 text-xs': true,
									}}>Required</Badge
								>
							{:else}
								<Badge variant="outline" class="text-xs">Optional</Badge>
							{/if}
						</TableCell>
						<TableCell>
							<Select.Root
								type="single"
								value={columnMapping[field.key]?.toString() || 'none'}
								onValueChange={(v) => updateMapping(field.key, v === 'none' ? null : parseInt(v))}
							>
								<Select.Trigger class="h-9 w-full">
									{#if hasMatchedColumn}
										{headers[columnMapping[field.key]!] ||
											`Column ${columnMapping[field.key]! + 1}`}
									{:else}
										None
									{/if}
								</Select.Trigger>
								<Select.Content>
									<Select.Group>
										<Select.Item value="none">None</Select.Item>
										{#each getAvailableHeaders() as header (header.value)}
											<Select.Item value={header.value.toString()}>{header.label}</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
						</TableCell>
					</TableRow>

					<!-- Sample data row -->
					{#if hasMatchedColumn}
						{@const sampleValues = getSampleValues(columnMapping[field.key]!)}
						{#if sampleValues.length > 0}
							<TableRow class="bg-muted/30 animate-in fade-in">
								<TableCell colspan={3} class="text-muted-foreground py-2 text-sm">
									<span class="font-medium">Sample values:</span>
									{sampleValues.join(', ')}
									{#if sampleValues.length >= 3}
										<span class="italic">...</span>
									{/if}
								</TableCell>
							</TableRow>
						{/if}
					{/if}
				{/each}
			</TableBody>
		</Table>
	</div>

	<div class="flex justify-between">
		<Button variant="outline" onclick={onBack}>Back</Button>
		<Button onclick={onNext} disabled={!validation.isValid}>Next: Classify Rows</Button>
	</div>
</div>
