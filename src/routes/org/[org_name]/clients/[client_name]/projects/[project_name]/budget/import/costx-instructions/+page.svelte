<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import { page } from '$app/state';

	const importLink = `/org/${page.data.project.client.organization.name}/clients/${page.data.project.client.name}/projects/${page.data.project.name}/budget/import`;
</script>

<svelte:head>
	<title>CostX Export Instructions</title>
</svelte:head>

<div class="px-4 py-8">
	<div class="mb-8">
		<Button variant="ghost" href={importLink} class="mb-4">
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back to Import
		</Button>

		<h1 class="mb-2 text-3xl font-bold">CostX Export Instructions</h1>
		<p class="text-muted-foreground">
			Follow these steps to export your CostX budget to an Excel file for import into the project
			controls system.
		</p>
	</div>

	<div class="max-w-xl space-y-8">
		<!-- Step 1 -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Step 1: Select a Workbook</h2>
			<p class="text-muted-foreground">
				Open CostX and select the workbook containing your budget data that you want to export.
			</p>
			<div class="bg-muted/50 rounded border p-4">
				<enhanced:img
					src="$lib/assets/costx/Step 1 - Select a workbook.png"
					alt="CostX interface showing workbook selection"
					class="w-full rounded border"
				/>
			</div>
		</div>

		<!-- Step 2 -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Step 2: Select the Workbooks Tab</h2>
			<p class="text-muted-foreground">
				Navigate to the "Workbooks" tab in the CostX interface to access export options.
			</p>
			<div class="bg-muted/50 rounded border p-4">
				<enhanced:img
					src="$lib/assets/costx/Step 2 - Select the _Workbooks_ tab.png"
					alt="CostX interface highlighting the Workbooks tab"
					class="w-full rounded border"
				/>
			</div>
		</div>

		<!-- Step 3 -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Step 3: Select Export</h2>
			<p class="text-muted-foreground">Click on the "Export" option to begin the export process.</p>
			<div class="bg-muted/50 rounded border p-4">
				<enhanced:img
					src="$lib/assets/costx/Step 3 - Select Export.png"
					alt="CostX interface showing the Export option"
					class="w-full rounded border"
				/>
			</div>
		</div>

		<!-- Step 4 -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Step 4: Select Export Workbook to Excel</h2>
			<p class="text-muted-foreground">
				Choose "Export Workbook to Excel" from the available export options.
			</p>
			<div class="bg-muted/50 rounded border p-4">
				<enhanced:img
					src="$lib/assets/costx/Step 4 - Select _Export Workbook to Excel_.png"
					alt="CostX export dialog showing Export Workbook to Excel option"
					class="w-full rounded border"
				/>
			</div>
		</div>

		<!-- Step 5 -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Step 5: Select Number of Levels</h2>
			<p class="text-muted-foreground">
				In the export dialog, locate the "Number of Levels" setting to configure how many levels of
				your Work Breakdown Structure (WBS) hierarchy to include.
			</p>
			<div class="bg-muted/50 rounded border p-4">
				<enhanced:img
					src="$lib/assets/costx/Step 5 - Select _Number of Levels_.png"
					alt="CostX export dialog highlighting the Number of Levels setting"
					class="w-full rounded border"
				/>
			</div>
		</div>

		<!-- Step 6 -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Step 6: Set Number of Levels</h2>
			<p class="text-muted-foreground">
				Set the number of levels to at least as high as the number of levels in your WBS hierarchy.
				Setting it to 10 should be safe for most projects and ensures all detail is captured.
			</p>
			<div class="bg-muted/50 rounded border p-4">
				<enhanced:img
					src="$lib/assets/costx/Step 6 - Set number of levels to some number at least as high the number of levels in your WBS. 10 should be safe..png"
					alt="CostX export dialog showing number of levels set to 10"
					class="w-full rounded border"
				/>
			</div>
		</div>

		<!-- Final Step -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Final Step: Export the File</h2>
			<p class="text-muted-foreground">
				Click the "Export" button to save your budget data as an Excel file. Choose a location and
				filename that you'll remember for the import process.
			</p>
			<div class="rounded border border-blue-200 bg-blue-50 p-4">
				<h3 class="mb-2 font-medium text-blue-900">Important Notes:</h3>
				<ul class="space-y-1 text-sm text-blue-800">
					<li>• The exported file will be in .xlsx format</li>
					<li>• Make sure to include enough levels to capture all your WBS detail</li>
					<li>
						• If you see yellow highlighted cells in the import preview, consider re-exporting with
						more levels
					</li>
					<li>
						• The file should contain columns for Code, Description, Quantity, UOM, Rate, SubTotal,
						Factor, and Total
					</li>
				</ul>
			</div>
		</div>

		<!-- Action Buttons -->
		<div class="flex gap-4 pt-6">
			<Button href={importLink} variant="outline">
				<ArrowLeft class="mr-2 h-4 w-4" />
				Back to Import
			</Button>
		</div>
	</div>
</div>
