<script lang="ts">
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { vendorSchema, vendorTypes, currencies, paymentTermsOptions } from '$lib/schemas/vendor';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { toast } from 'svelte-sonner';

	const { data }: { data: PageData } = $props();
	const { organizations, clients, projects } = data;

	const form = superForm(data.form, {
		validators: zodClient(vendorSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	// Reactive variables for hierarchical selection
	let selectedEntityType = $state<'organization' | 'client' | 'project'>('organization');
	let selectedOrganization = $state<string>('');
	let selectedClient = $state<string>('');
	let selectedProject = $state<string>('');

	// Local variables for select components that need string values
	let vendorTypeValue = $state<(typeof vendorTypes)[number] | ''>('');
	let currencyValue = $state<(typeof currencies)[number]>('USD');
	let paymentTermsValue = $state<(typeof paymentTermsOptions)[number] | ''>('');

	// Update form data when selections change
	$effect(() => {
		// Clear all entity IDs first
		$formData.org_id = null;
		$formData.client_id = null;
		$formData.project_id = null;

		// Set the appropriate entity ID based on selection
		if (selectedEntityType === 'organization' && selectedOrganization) {
			$formData.org_id = selectedOrganization;
		} else if (selectedEntityType === 'client' && selectedClient) {
			$formData.client_id = selectedClient;
		} else if (selectedEntityType === 'project' && selectedProject) {
			$formData.project_id = selectedProject;
		}
	});

	// Sync local select values with form data
	$effect(() => {
		$formData.vendor_type = vendorTypeValue || null;
	});

	$effect(() => {
		$formData.currency = currencyValue;
	});

	$effect(() => {
		$formData.payment_terms = paymentTermsValue || null;
	});

	// Filter clients based on selected organization
	const filteredClients = $derived(() => {
		if (selectedEntityType !== 'client' || !selectedOrganization) return clients;
		return clients.filter((c) => {
			// The client data includes organization as a joined field
			const clientData = c.client as any;
			return clientData?.organization?.org_id === selectedOrganization;
		});
	});

	// Filter projects based on selected client
	const filteredProjects = $derived(() => {
		if (selectedEntityType !== 'project' || !selectedClient) return projects;
		return projects.filter((p) => {
			// The project data includes client as a joined field
			const projectData = p.project as any;
			return projectData?.client?.client_id === selectedClient;
		});
	});
</script>

<div class="container mx-auto max-w-2xl py-8">
	<div class="mb-8 space-y-4">
		<div class="space-y-2">
			<h1 class="text-3xl font-bold tracking-tight">Create a New Vendor</h1>
		</div>
	</div>

	<div class="rounded-lg border p-6 shadow-sm">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Access Level Selection -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Access Level</h3>
					<p class="text-muted-foreground text-sm">
						Choose where this vendor should be available. Organization-level vendors are available
						to all clients and projects, client-level vendors are available to all projects within
						that client, and project-level vendors are only available to the specific project.
					</p>

					<!-- Entity Type Selection -->
					<div class="grid grid-cols-3 gap-4">
						{#if organizations.length > 0}
							<label class="flex items-center space-x-2">
								<input
									type="radio"
									bind:group={selectedEntityType}
									value="organization"
									class="text-primary"
								/>
								<span>Organization Level</span>
							</label>
						{/if}
						{#if clients.length > 0}
							<label class="flex items-center space-x-2">
								<input
									type="radio"
									bind:group={selectedEntityType}
									value="client"
									class="text-primary"
								/>
								<span>Client Level</span>
							</label>
						{/if}
						{#if projects.length > 0}
							<label class="flex items-center space-x-2">
								<input
									type="radio"
									bind:group={selectedEntityType}
									value="project"
									class="text-primary"
								/>
								<span>Project Level</span>
							</label>
						{/if}
					</div>

					<!-- Organization Selection -->
					{#if selectedEntityType === 'organization' && organizations.length > 0}
						<Form.Field {form} name="org_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Organization <span class="text-red-500">*</span></Form.Label>
									<Select.Root type="single" bind:value={selectedOrganization} name={props.name}>
										<Select.Trigger {...props}>
											{selectedOrganization
												? organizations.find((o) => o.organization?.org_id === selectedOrganization)
														?.organization?.name
												: 'Select organization'}
										</Select.Trigger>
										<Select.Content>
											{#each organizations as org (org.organization?.org_id)}
												{#if org.organization?.org_id}
													<Select.Item value={org.organization.org_id}>
														{org.organization.name}
													</Select.Item>
												{/if}
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					{/if}

					<!-- Client Selection -->
					{#if selectedEntityType === 'client'}
						<!-- Organization selection for filtering -->
						<div>
							<label for="org-filter" class="text-sm font-medium">Filter by Organization</label>
							<Select.Root
								type="single"
								bind:value={selectedOrganization}
								onValueChange={() => {
									selectedClient = ''; // Reset client selection
								}}
							>
								<input type="hidden" id="org-filter" />
								<Select.Trigger>
									{selectedOrganization
										? organizations.find((o) => o.organization?.org_id === selectedOrganization)
												?.organization?.name
										: 'All organizations'}
								</Select.Trigger>
								<Select.Content>
									<Select.Item value="">All organizations</Select.Item>
									{#each organizations as org (org.organization?.org_id)}
										{#if org.organization?.org_id}
											<Select.Item value={org.organization.org_id}>
												{org.organization.name}
											</Select.Item>
										{/if}
									{/each}
								</Select.Content>
							</Select.Root>
						</div>

						<Form.Field {form} name="client_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Client <span class="text-red-500">*</span></Form.Label>
									<Select.Root type="single" bind:value={selectedClient} name={props.name}>
										<Select.Trigger {...props}>
											{selectedClient
												? filteredClients().find((c) => c.client?.client_id === selectedClient)
														?.client?.name
												: 'Select client'}
										</Select.Trigger>
										<Select.Content>
											{#each filteredClients() as client (client.client?.client_id)}
												{#if client.client?.client_id}
													<Select.Item value={client.client.client_id}>
														{client.client.name}
														{#if (client.client as any)?.organization}
															<span class="text-muted-foreground text-sm">
																({(client.client as any).organization.name})
															</span>
														{/if}
													</Select.Item>
												{/if}
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					{/if}

					<!-- Project Selection -->
					{#if selectedEntityType === 'project'}
						<!-- Client selection for filtering -->
						<div>
							<label for="client-filter" class="text-sm font-medium">Filter by Client</label>
							<Select.Root
								type="single"
								bind:value={selectedClient}
								onValueChange={() => {
									selectedProject = ''; // Reset project selection
								}}
							>
								<input type="hidden" id="client-filter" />
								<Select.Trigger>
									{selectedClient
										? clients.find((c) => c.client?.client_id === selectedClient)?.client?.name
										: 'All clients'}
								</Select.Trigger>
								<Select.Content>
									<Select.Item value="">All clients</Select.Item>
									{#each clients as client (client.client?.client_id)}
										{#if client.client}
											<Select.Item value={client.client.client_id}>
												{client.client.name}
												{#if client.client.organization}
													<span class="text-muted-foreground text-sm">
														({client.client.organization.name})
													</span>
												{/if}
											</Select.Item>
										{/if}
									{/each}
								</Select.Content>
							</Select.Root>
						</div>

						<Form.Field {form} name="project_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Project <span class="text-red-500">*</span></Form.Label>
									<Select.Root type="single" bind:value={selectedProject} name={props.name}>
										<Select.Trigger {...props}>
											{selectedProject
												? filteredProjects().find((p) => p.project?.project_id === selectedProject)
														?.project?.name
												: 'Select project'}
										</Select.Trigger>
										<Select.Content>
											{#each filteredProjects() as project (project.project?.project_id)}
												{#if project.project}
													<Select.Item value={project.project.project_id}>
														{project.project.name}
														{#if project.project.client}
															<span class="text-muted-foreground text-sm">
																({project.project.client.name})
															</span>
														{/if}
													</Select.Item>
												{/if}
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					{/if}
				</div>

				<!-- Basic Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Basic Information</h3>

					<Form.Field {form} name="name">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Vendor Name <span class="text-red-500">*</span></Form.Label>
								<Input
									{...props}
									type="text"
									placeholder="Enter vendor name"
									bind:value={$formData.name}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea
									{...props}
									placeholder="Brief description of the vendor"
									bind:value={$formData.description}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="vendor_type">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Vendor Type</Form.Label>
								<Select.Root type="single" bind:value={vendorTypeValue} name={props.name}>
									<Select.Trigger {...props}>
										{vendorTypeValue || 'Select vendor type'}
									</Select.Trigger>
									<Select.Content>
										{#each vendorTypes as type (type)}
											<Select.Item value={type}>{type}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Contact Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Contact Information</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="contact_name">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Contact Name</Form.Label>
									<Input
										{...props}
										type="text"
										placeholder="Primary contact name"
										bind:value={$formData.contact_name}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="contact_email">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Contact Email</Form.Label>
									<Input
										{...props}
										type="email"
										placeholder="<EMAIL>"
										bind:value={$formData.contact_email}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="contact_phone">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Contact Phone</Form.Label>
									<Input
										{...props}
										type="tel"
										placeholder="+****************"
										bind:value={$formData.contact_phone}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="website">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Website</Form.Label>
									<Input
										{...props}
										type="url"
										placeholder="https://vendor.com"
										bind:value={$formData.website}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<Form.Field {form} name="contact_address">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Address</Form.Label>
								<Textarea
									{...props}
									placeholder="Full address"
									bind:value={$formData.contact_address}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Financial Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Financial Information</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="tax_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Tax ID</Form.Label>
									<Input
										{...props}
										type="text"
										placeholder="Tax identification number"
										bind:value={$formData.tax_id}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="currency">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Currency</Form.Label>
									<Select.Root type="single" bind:value={currencyValue} name={props.name}>
										<Select.Trigger {...props}>
											{currencyValue}
										</Select.Trigger>
										<Select.Content>
											{#each currencies as currency (currency)}
												<Select.Item value={currency}>{currency}</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="payment_terms">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Payment Terms</Form.Label>
									<Select.Root type="single" bind:value={paymentTermsValue} name={props.name}>
										<Select.Trigger {...props}>
											{paymentTermsValue || 'Select payment terms'}
										</Select.Trigger>
										<Select.Content>
											{#each paymentTermsOptions as terms (terms)}
												<Select.Item value={terms}>{terms}</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="payment_terms_days">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Payment Terms (Days)</Form.Label>
									<Input
										{...props}
										type="number"
										min="0"
										max="365"
										placeholder="30"
										bind:value={$formData.payment_terms_days}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="credit_limit">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Credit Limit</Form.Label>
									<Input
										{...props}
										type="number"
										min="0"
										step="0.01"
										placeholder="0.00"
										bind:value={$formData.credit_limit}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>
				</div>

				<!-- Status -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Status</h3>

					<Form.Field {form} name="is_active">
						<Form.Control>
							{#snippet children({ props })}
								<div class="flex items-center space-x-2">
									<Checkbox {...props} bind:checked={$formData.is_active} />
									<Form.Label>Active vendor</Form.Label>
								</div>
								<Form.Description>
									Inactive vendors will not appear in vendor selection lists
								</Form.Description>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Submit Button -->
				<div class="flex justify-end space-x-4 pt-6">
					<Button type="button" variant="outline" href="/vendor">Cancel</Button>
					<Form.Button>Create Vendor</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
