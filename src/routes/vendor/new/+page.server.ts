import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { vendorSchema } from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';
import type { Tables } from '$lib/database.types';

export const load: PageServerLoad = async ({ locals, cookies }) => {
	const { user } = await requireUser(cookies);
	const { supabase } = locals;

	// Get user's accessible organizations for hierarchical selection
	const { data: orgMemberships } = await supabase
		.from('membership')
		.select('entity_id, role')
		.eq('user_id', user.id)
		.eq('entity_type', 'organization')
		.gte('role', 'admin'); // Only admins can create org-level vendors

	const { data: clientMemberships } = await supabase
		.from('membership')
		.select('entity_id, role')
		.eq('user_id', user.id)
		.eq('entity_type', 'client')
		.gte('role', 'admin'); // Only admins can create client-level vendors

	const { data: projectMemberships } = await supabase
		.from('membership')
		.select('entity_id, role')
		.eq('user_id', user.id)
		.eq('entity_type', 'project')
		.gte('role', 'editor'); // Editors can create project-level vendors

	// Get organization details
	let organizations: { organization: Partial<Tables<'organization'>>; role: string | undefined }[] =
		[];
	if (orgMemberships?.length) {
		const { data: orgs } = await supabase
			.from('organization')
			.select('org_id, name')
			.in(
				'org_id',
				orgMemberships.map((m) => m.entity_id),
			);
		organizations =
			orgs?.map((org) => ({
				organization: org,
				role: orgMemberships.find((m) => m.entity_id === org.org_id)?.role,
			})) || [];
	}

	// Get client details
	let clients: { client: Partial<Tables<'client'>>; role: string | undefined }[] = [];
	if (clientMemberships?.length) {
		const { data: clientData } = await supabase
			.from('client')
			.select('client_id, name, organization(org_id, name)')
			.in(
				'client_id',
				clientMemberships.map((m) => m.entity_id),
			);
		clients =
			clientData?.map((client) => ({
				client,
				role: clientMemberships.find((m) => m.entity_id === client.client_id)?.role,
			})) || [];
	}

	// Get project details
	let projects: { project: Partial<Tables<'project'>>; role: string | undefined }[] = [];
	if (projectMemberships?.length) {
		const { data: projectData } = await supabase
			.from('project')
			.select('project_id, name, client(client_id, name, organization(org_id, name))')
			.in(
				'project_id',
				projectMemberships.map((m) => m.entity_id),
			);
		projects =
			projectData?.map((project) => ({
				project,
				role: projectMemberships.find((m) => m.entity_id === project.project_id)?.role,
			})) || [];
	}

	const form = await superValidate(zod(vendorSchema));

	return {
		form,
		organizations,
		clients,
		projects,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies }) => {
		const { user } = await requireUser(cookies);
		const { supabase } = locals;

		const form = await superValidate(request, zod(vendorSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Prepare the vendor data
		const vendorData = {
			...form.data,
			created_by_user_id: user.id,
		};

		// Insert the vendor
		const { data: vendor, error: vendorError } = await supabase
			.from('vendor')
			.insert(vendorData)
			.select('vendor_id, name')
			.single();

		if (vendorError) {
			console.error('Error creating vendor:', vendorError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create vendor' },
			});
		}

		throw redirect(
			'/vendor',
			{
				type: 'success',
				message: `Vendor "${vendor.name}" created successfully`,
			},
			cookies,
		);
	},
};
