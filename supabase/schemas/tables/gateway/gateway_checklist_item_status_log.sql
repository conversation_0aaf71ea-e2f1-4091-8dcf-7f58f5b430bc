-- Gateway Checklist Item Status Log Table Schema
-- Status history log for gateway checklist items with temporal tracking
-- Gateway Checklist Item Status Log table
CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item_status_log" (
	"log_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"gateway_checklist_item_id" "uuid" NOT NULL,
	"status" "public"."checklist_item_status" DEFAULT 'Incomplete'::"public"."checklist_item_status" NOT NULL,
	"updated_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"valid_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"latest" boolean DEFAULT false NOT NULL
);

ALTER TABLE "public"."gateway_checklist_item_status_log" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item_status_log" IS 'Status history log for gateway checklist items with temporal tracking';

-- Primary key constraint
ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_log_pkey" PRIMARY KEY ("log_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_lo_gateway_checklist_item_id_fkey" FOREIGN KEY ("gateway_checklist_item_id") REFERENCES "public"."gateway_checklist_item" ("gateway_checklist_item_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_log_updated_by_user_id_fkey" FOREIGN KEY ("updated_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "gateway_checklist_item_status_gateway_checklist_item_id_val_idx" ON "public"."gateway_checklist_item_status_log" USING "btree" ("gateway_checklist_item_id", "valid_at" DESC);

CREATE UNIQUE INDEX "gateway_checklist_item_status_log_gateway_checklist_item_id_idx" ON "public"."gateway_checklist_item_status_log" USING "btree" ("gateway_checklist_item_id")
WHERE
	("latest" = true);

-- Enable Row Level Security
ALTER TABLE "public"."gateway_checklist_item_status_log" ENABLE ROW LEVEL SECURITY;

-- Function to set latest status flag
CREATE OR REPLACE FUNCTION public.set_gateway_checklist_item_latest () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
BEGIN
  -- 1) Un-flag every other entry for this checklist item
  UPDATE public.gateway_checklist_item_status_log
  SET latest = FALSE
  WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
    AND log_id != NEW.log_id;

  -- 2) Flag whichever row truly has the most recent valid_at
  UPDATE public.gateway_checklist_item_status_log
  SET latest = TRUE
  WHERE log_id = (
    SELECT log_id
    FROM public.gateway_checklist_item_status_log
    WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
    ORDER BY valid_at DESC
    LIMIT 1
  );

  RETURN NULL;  -- trigger is AFTER, so no row returned
END;
$$;

ALTER FUNCTION "public"."set_gateway_checklist_item_latest" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."set_gateway_checklist_item_latest" () IS 'Ensures only one status log entry per checklist item is marked as latest';

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."gateway_checklist_item_status_log" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Trigger to manage latest status flag
CREATE OR REPLACE TRIGGER "trg_gateway_checklist_item_status_insert"
AFTER INSERT ON "public"."gateway_checklist_item_status_log" FOR EACH ROW
EXECUTE FUNCTION "public"."set_gateway_checklist_item_latest" ();

-- Row Level Security Policies
CREATE POLICY "Project editors can insert gateway checklist item status log" ON "public"."gateway_checklist_item_status_log" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"gateway_checklist_item"."project_stage_id"
									FROM
										"public"."gateway_checklist_item"
									WHERE
										(
											"gateway_checklist_item"."gateway_checklist_item_id" = "gateway_checklist_item_status_log"."gateway_checklist_item_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update gateway checklist item status log" ON "public"."gateway_checklist_item_status_log"
FOR UPDATE
	TO "authenticated" USING ((FALSE))
WITH
	CHECK ((FALSE));

CREATE POLICY "Project editors can delete gateway checklist item status log" ON "public"."gateway_checklist_item_status_log" FOR DELETE TO "authenticated" USING ((FALSE));

CREATE POLICY "Project viewers can view gateway checklist item status log" ON "public"."gateway_checklist_item_status_log" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"gateway_checklist_item"."project_stage_id"
									FROM
										"public"."gateway_checklist_item"
									WHERE
										(
											"gateway_checklist_item"."gateway_checklist_item_id" = "gateway_checklist_item_status_log"."gateway_checklist_item_id"
										)
								)
							)
					)
				) AS "can_access_project"
		)
	);
