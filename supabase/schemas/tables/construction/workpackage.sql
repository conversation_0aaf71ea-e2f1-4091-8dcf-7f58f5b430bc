-- Work Package table
CREATE TABLE IF NOT EXISTS "public"."work_package" (
	"work_package_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"project_id" "uuid" NOT NULL,
	"parent_work_package_id" "uuid",
	"purchase_order_id" "uuid",
	"wbs_library_item_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);
