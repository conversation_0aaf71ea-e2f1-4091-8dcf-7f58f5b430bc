-- Purchase Order Table
CREATE TABLE IF NOT EXISTS "public"."purchase_order" (
	"purchase_order_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"po_number" "text" NOT NULL,
	"description" "text",
	"project_id" "uuid" NOT NULL,
	"vendor_id" "uuid" NOT NULL,
	"account" "text",
	"original_amount" numeric(15, 2),
	"co_amount" numeric(15, 2),
	"freight" numeric(15, 2),
	"tax" numeric(15, 2),
	"other" numeric(15, 2),
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);
