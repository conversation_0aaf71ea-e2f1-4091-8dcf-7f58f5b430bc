-- Risk Register Audit Table Schema
-- Audit log of all changes to risk register entries
-- Risk Register Audit table
CREATE TABLE IF NOT EXISTS "public"."risk_register_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"risk_id" "uuid",
	"project_id" "uuid",
	"title" "text",
	"description" "text",
	"status" "text",
	"wbs_library_item_id" "uuid",
	"date_identified" "date",
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"probability" numeric(5, 2),
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "risk_register_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."risk_register_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."risk_register_audit" IS 'Audit log of all changes to risk register entries';

-- Primary key constraint
ALTER TABLE ONLY "public"."risk_register_audit"
ADD CONSTRAINT "risk_register_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."risk_register_audit"
ADD CONSTRAINT "risk_register_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "risk_register_audit_changed_at_idx" ON "public"."risk_register_audit" USING "btree" ("changed_at");

CREATE INDEX "risk_register_audit_risk_id_idx" ON "public"."risk_register_audit" USING "btree" ("risk_id");

CREATE INDEX "risk_register_audit_changed_by_idx" ON "public"."risk_register_audit" USING "btree" ("changed_by");

CREATE INDEX "risk_register_audit_project_id_idx" ON "public"."risk_register_audit" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."risk_register_audit" ENABLE ROW LEVEL SECURITY;

-- Risk Register Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_risk_register_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.risk_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.cause, OLD.effect, OLD.program_impact, OLD.probability, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_risk_register_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_risk_register_changes" () IS 'Audit trigger function for risk_register table';

-- Audit trigger for risk_register table
CREATE OR REPLACE TRIGGER "audit_risk_register_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."risk_register" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_risk_register_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert risk register audit records" ON "public"."risk_register_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view risk register audit for accessible projects" ON "public"."risk_register_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
